import torch

# ======================
# 非并行版本（基准）
# ======================
def non_parallel_matmul():
    # 定义两个大矩阵（假设单 GPU 放不下 B，需要拆分）
    # 这里为了演示，用小矩阵（实际中可能是 10000x10000）
    A = torch.randn(8, 4, device='cpu')  # 形状 [8,4]
    B = torch.randn(4, 8, device='cpu')  # 形状 [4,8]
    result = A @ B  # 直接计算矩阵乘法
    return result

# 运行非并行版本
non_parallel_result = non_parallel_matmul()
print("非并行结果形状:", non_parallel_result.shape)  # 输出 [8,8]
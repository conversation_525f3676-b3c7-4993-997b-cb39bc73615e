import time
import random
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    from matplotlib.animation import FuncAnimation
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False
    print("警告: matplotlib未安装，将跳过可视化功能")

try:
    from tqdm import tqdm
    HAS_TQDM = True
except ImportError:
    HAS_TQDM = False
    print("警告: tqdm未安装，将使用简单进度显示")

# ======================
# 核心数据结构和枚举
# ======================

class ParallelStrategy(Enum):
    """并行策略枚举"""
    NONE = "无并行"
    DATA_PARALLEL = "数据并行"
    MODEL_PARALLEL = "模型并行"
    PIPELINE_PARALLEL = "流水线并行"
    TENSOR_PARALLEL = "张量并行"

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    strategy: ParallelStrategy
    total_time: float
    compute_time: float
    communication_time: float
    memory_usage: float
    throughput: float
    speedup: float
    efficiency: float
    details: Dict[str, Any]

@dataclass
class SimulationConfig:
    """模拟配置数据类"""
    matrix_size: int = 1000
    batch_size: int = 32
    num_devices: int = 4
    model_layers: int = 8
    pipeline_stages: int = 4
    tensor_split_dim: int = 2
    base_compute_time: float = 2.0  # 增加基础计算时间
    communication_overhead: float = 0.05  # 减少通信开销
    memory_limit_per_device: float = 1000.0

# ======================
# 核心类定义
# ======================

class PerformanceTracker:
    """性能追踪器"""

    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.current_start_time = None

    def start_timing(self):
        """开始计时"""
        self.current_start_time = time.time()

    def end_timing(self) -> float:
        """结束计时并返回耗时"""
        if self.current_start_time is None:
            return 0.0
        return time.time() - self.current_start_time

    def add_metrics(self, metrics: PerformanceMetrics):
        """添加性能指标"""
        self.metrics.append(metrics)

    def get_baseline_time(self) -> float:
        """获取基准时间（无并行）"""
        for metric in self.metrics:
            if metric.strategy == ParallelStrategy.NONE:
                return metric.total_time
        return 1.0  # 默认基准时间

    def calculate_speedup(self, current_time: float) -> float:
        """计算加速比"""
        baseline = self.get_baseline_time()
        return baseline / current_time if current_time > 0 else 1.0

    def calculate_efficiency(self, speedup: float, num_devices: int) -> float:
        """计算并行效率"""
        return speedup / num_devices if num_devices > 0 else 0.0


class Visualizer:
    """结果可视化器"""

    def __init__(self):
        self.has_matplotlib = HAS_MATPLOTLIB

    def print_progress(self, current: int, total: int, prefix: str = "进度"):
        """打印进度条（无tqdm时的备选方案）"""
        if HAS_TQDM:
            return  # tqdm会处理进度显示

        percent = (current / total) * 100
        bar_length = 30
        filled_length = int(bar_length * current // total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        print(f'\r{prefix}: |{bar}| {percent:.1f}% ({current}/{total})', end='', flush=True)
        if current == total:
            print()  # 完成时换行

    def show_performance_comparison(self, metrics: List[PerformanceMetrics]):
        """显示性能对比"""
        if not self.has_matplotlib:
            self._print_performance_table(metrics)
            return

        # 创建性能对比图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('并行策略性能对比分析', fontsize=16, fontweight='bold')

        strategies = [m.strategy.value for m in metrics]
        times = [m.total_time for m in metrics]
        speedups = [m.speedup for m in metrics]
        efficiencies = [m.efficiency for m in metrics]
        throughputs = [m.throughput for m in metrics]

        # 执行时间对比
        bars1 = ax1.bar(strategies, times, color=['red', 'blue', 'green', 'orange', 'purple'][:len(strategies)])
        ax1.set_title('执行时间对比')
        ax1.set_ylabel('时间 (秒)')
        ax1.tick_params(axis='x', rotation=45)
        for i, bar in enumerate(bars1):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}s', ha='center', va='bottom')

        # 加速比对比
        bars2 = ax2.bar(strategies, speedups, color=['red', 'blue', 'green', 'orange', 'purple'][:len(strategies)])
        ax2.set_title('加速比对比')
        ax2.set_ylabel('加速比')
        ax2.tick_params(axis='x', rotation=45)
        ax2.axhline(y=1, color='red', linestyle='--', alpha=0.7, label='基准线')
        for i, bar in enumerate(bars2):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.2f}x', ha='center', va='bottom')

        # 并行效率对比
        bars3 = ax3.bar(strategies, efficiencies, color=['red', 'blue', 'green', 'orange', 'purple'][:len(strategies)])
        ax3.set_title('并行效率对比')
        ax3.set_ylabel('效率 (%)')
        ax3.tick_params(axis='x', rotation=45)
        for i, bar in enumerate(bars3):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.1f}%', ha='center', va='bottom')

        # 吞吐量对比
        bars4 = ax4.bar(strategies, throughputs, color=['red', 'blue', 'green', 'orange', 'purple'][:len(strategies)])
        ax4.set_title('吞吐量对比')
        ax4.set_ylabel('吞吐量 (ops/s)')
        ax4.tick_params(axis='x', rotation=45)
        for i, bar in enumerate(bars4):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.1f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.show()

    def _print_performance_table(self, metrics: List[PerformanceMetrics]):
        """打印性能对比表格（无matplotlib时的备选方案）"""
        print("\n" + "="*80)
        print("并行策略性能对比分析")
        print("="*80)
        print(f"{'策略':<15} {'时间(s)':<10} {'加速比':<10} {'效率(%)':<10} {'吞吐量':<10}")
        print("-"*80)

        for metric in metrics:
            print(f"{metric.strategy.value:<15} "
                  f"{metric.total_time:<10.2f} "
                  f"{metric.speedup:<10.2f} "
                  f"{metric.efficiency:<10.1f} "
                  f"{metric.throughput:<10.1f}")
        print("="*80)

    def show_timeline_visualization(self, strategy: ParallelStrategy, timeline_data: List[Dict]):
        """显示时间线可视化"""
        if not self.has_matplotlib or not timeline_data:
            return

        fig, ax = plt.subplots(figsize=(12, 6))
        ax.set_title(f'{strategy.value} - 执行时间线')

        colors = ['blue', 'green', 'red', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, data in enumerate(timeline_data):
            device_id = data.get('device_id', i)
            start_time = data.get('start_time', 0)
            duration = data.get('duration', 1)
            task_name = data.get('task_name', f'Task {i}')

            # 绘制任务条
            rect = patches.Rectangle((start_time, device_id), duration, 0.8,
                                   linewidth=1, edgecolor='black',
                                   facecolor=colors[device_id % len(colors)],
                                   alpha=0.7)
            ax.add_patch(rect)

            # 添加任务标签
            ax.text(start_time + duration/2, device_id + 0.4, task_name,
                   ha='center', va='center', fontsize=8, fontweight='bold')

        ax.set_xlabel('时间 (秒)')
        ax.set_ylabel('设备/进程 ID')
        ax.set_ylim(-0.5, len(timeline_data) - 0.5)
        ax.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()


class ParallelSimulator:
    """并行计算模拟器"""

    def __init__(self, config: SimulationConfig = None):
        self.config = config or SimulationConfig()
        self.tracker = PerformanceTracker()
        self.visualizer = Visualizer()

    def simulate_computation(self, duration: float, device_id: int = 0) -> float:
        """模拟计算过程"""
        # 添加一些随机性来模拟真实世界的变化
        actual_duration = duration * (0.95 + 0.1 * random.random())
        time.sleep(actual_duration * 0.005)  # 实际等待时间缩短200倍用于演示
        return actual_duration

    def simulate_communication(self, data_size: float, bandwidth: float = 1.0) -> float:
        """模拟通信开销"""
        comm_time = data_size / bandwidth * self.config.communication_overhead
        time.sleep(comm_time * 0.005)  # 实际等待时间缩短200倍用于演示
        return comm_time

    def run_baseline(self) -> PerformanceMetrics:
        """运行基准测试（无并行）"""
        print("\n🔄 运行基准测试（无并行）...")

        self.tracker.start_timing()

        # 模拟大型矩阵乘法计算
        total_ops = self.config.matrix_size ** 2 * self.config.batch_size
        compute_time = self.simulate_computation(self.config.base_compute_time)

        total_time = self.tracker.end_timing()

        metrics = PerformanceMetrics(
            strategy=ParallelStrategy.NONE,
            total_time=total_time,
            compute_time=compute_time,
            communication_time=0.0,
            memory_usage=total_ops * 4 / (1024**2),  # MB
            throughput=total_ops / total_time,
            speedup=1.0,
            efficiency=100.0,
            details={"total_operations": total_ops}
        )

        self.tracker.add_metrics(metrics)
        print(f"✅ 基准测试完成: {total_time:.2f}秒")
        return metrics

    def run_data_parallel(self) -> PerformanceMetrics:
        """运行数据并行模拟"""
        print(f"\n🔄 运行数据并行模拟 ({self.config.num_devices} 设备)...")

        self.tracker.start_timing()
        timeline_data = []

        # 将批次数据分割到多个设备
        batch_per_device = self.config.batch_size // self.config.num_devices
        total_ops = self.config.matrix_size ** 2 * self.config.batch_size

        # 模拟数据分发
        data_size = total_ops * 4 / (1024**2)  # MB
        scatter_time = self.simulate_communication(data_size / self.config.num_devices)

        # 并行计算（在单机上用循环模拟）
        device_compute_times = []
        max_compute_time = 0

        if HAS_TQDM:
            devices = tqdm(range(self.config.num_devices), desc="设备计算")
        else:
            devices = range(self.config.num_devices)

        for device_id in devices:
            if not HAS_TQDM:
                self.visualizer.print_progress(device_id + 1, self.config.num_devices, "数据并行")

            # 每个设备处理部分数据（理想情况下应该是并行的）
            device_ops = self.config.matrix_size ** 2 * batch_per_device
            compute_time = self.simulate_computation(
                self.config.base_compute_time / self.config.num_devices * 0.8  # 80%的理想加速
            )
            device_compute_times.append(compute_time)
            max_compute_time = max(max_compute_time, compute_time)

            timeline_data.append({
                'device_id': device_id,
                'start_time': scatter_time,
                'duration': compute_time,
                'task_name': f'Batch {device_id}'
            })

        # 模拟结果收集
        gather_time = self.simulate_communication(data_size / self.config.num_devices)

        total_time = self.tracker.end_timing()
        speedup = self.tracker.calculate_speedup(total_time)
        efficiency = self.tracker.calculate_efficiency(speedup, self.config.num_devices)

        metrics = PerformanceMetrics(
            strategy=ParallelStrategy.DATA_PARALLEL,
            total_time=total_time,
            compute_time=max_compute_time,
            communication_time=scatter_time + gather_time,
            memory_usage=data_size / self.config.num_devices,
            throughput=total_ops / total_time,
            speedup=speedup,
            efficiency=efficiency * 100,
            details={
                "num_devices": self.config.num_devices,
                "batch_per_device": batch_per_device,
                "scatter_time": scatter_time,
                "gather_time": gather_time,
                "timeline": timeline_data
            }
        )

        self.tracker.add_metrics(metrics)
        print(f"✅ 数据并行完成: {total_time:.2f}秒, 加速比: {speedup:.2f}x")

        # 显示时间线
        self.visualizer.show_timeline_visualization(ParallelStrategy.DATA_PARALLEL, timeline_data)

        return metrics

    def run_model_parallel(self) -> PerformanceMetrics:
        """运行模型并行模拟"""
        print(f"\n🔄 运行模型并行模拟 ({self.config.model_layers} 层模型)...")

        self.tracker.start_timing()
        timeline_data = []

        total_ops = self.config.matrix_size ** 2 * self.config.batch_size
        layers_per_device = self.config.model_layers // self.config.num_devices

        total_compute_time = 0
        total_comm_time = 0
        current_time = 0

        if HAS_TQDM:
            devices = tqdm(range(self.config.num_devices), desc="模型层计算")
        else:
            devices = range(self.config.num_devices)

        for device_id in devices:
            if not HAS_TQDM:
                self.visualizer.print_progress(device_id + 1, self.config.num_devices, "模型并行")

            # 每个设备处理几层模型
            layer_compute_time = self.simulate_computation(
                self.config.base_compute_time / self.config.num_devices
            )
            total_compute_time += layer_compute_time

            # 层间通信（除了最后一个设备）
            if device_id < self.config.num_devices - 1:
                comm_time = self.simulate_communication(
                    total_ops * 4 / (1024**2) / self.config.num_devices
                )
                total_comm_time += comm_time
            else:
                comm_time = 0

            timeline_data.append({
                'device_id': device_id,
                'start_time': current_time,
                'duration': layer_compute_time,
                'task_name': f'Layers {device_id*layers_per_device}-{(device_id+1)*layers_per_device-1}'
            })

            current_time += layer_compute_time + comm_time

        total_time = self.tracker.end_timing()
        speedup = self.tracker.calculate_speedup(total_time)
        efficiency = self.tracker.calculate_efficiency(speedup, self.config.num_devices)

        metrics = PerformanceMetrics(
            strategy=ParallelStrategy.MODEL_PARALLEL,
            total_time=total_time,
            compute_time=total_compute_time,
            communication_time=total_comm_time,
            memory_usage=total_ops * 4 / (1024**2) / self.config.num_devices,
            throughput=total_ops / total_time,
            speedup=speedup,
            efficiency=efficiency * 100,
            details={
                "num_devices": self.config.num_devices,
                "layers_per_device": layers_per_device,
                "total_layers": self.config.model_layers,
                "timeline": timeline_data
            }
        )

        self.tracker.add_metrics(metrics)
        print(f"✅ 模型并行完成: {total_time:.2f}秒, 加速比: {speedup:.2f}x")

        # 显示时间线
        self.visualizer.show_timeline_visualization(ParallelStrategy.MODEL_PARALLEL, timeline_data)

        return metrics

    def run_pipeline_parallel(self) -> PerformanceMetrics:
        """运行流水线并行模拟"""
        print(f"\n🔄 运行流水线并行模拟 ({self.config.pipeline_stages} 阶段)...")

        self.tracker.start_timing()
        timeline_data = []

        total_ops = self.config.matrix_size ** 2 * self.config.batch_size
        batches = self.config.batch_size // 4  # 将批次进一步细分用于流水线
        stage_time = self.config.base_compute_time / self.config.pipeline_stages

        # 流水线执行：每个阶段处理不同的微批次
        max_time = 0
        total_comm_time = 0

        if HAS_TQDM:
            stages = tqdm(range(self.config.pipeline_stages), desc="流水线阶段")
        else:
            stages = range(self.config.pipeline_stages)

        for stage_id in stages:
            if not HAS_TQDM:
                self.visualizer.print_progress(stage_id + 1, self.config.pipeline_stages, "流水线并行")

            # 每个阶段的开始时间（考虑流水线重叠）
            start_time = stage_id * stage_time * 0.7  # 70%重叠

            # 处理多个微批次
            for batch_id in range(batches):
                batch_start = start_time + batch_id * stage_time
                compute_time = self.simulate_computation(stage_time)

                timeline_data.append({
                    'device_id': stage_id,
                    'start_time': batch_start,
                    'duration': compute_time,
                    'task_name': f'Stage{stage_id}-Batch{batch_id}'
                })

                max_time = max(max_time, batch_start + compute_time)

            # 阶段间通信
            if stage_id < self.config.pipeline_stages - 1:
                comm_time = self.simulate_communication(
                    total_ops * 4 / (1024**2) / self.config.pipeline_stages
                )
                total_comm_time += comm_time

        total_time = self.tracker.end_timing()
        speedup = self.tracker.calculate_speedup(total_time)
        efficiency = self.tracker.calculate_efficiency(speedup, self.config.pipeline_stages)

        metrics = PerformanceMetrics(
            strategy=ParallelStrategy.PIPELINE_PARALLEL,
            total_time=total_time,
            compute_time=max_time,
            communication_time=total_comm_time,
            memory_usage=total_ops * 4 / (1024**2) / self.config.pipeline_stages,
            throughput=total_ops / total_time,
            speedup=speedup,
            efficiency=efficiency * 100,
            details={
                "pipeline_stages": self.config.pipeline_stages,
                "micro_batches": batches,
                "overlap_ratio": 0.7,
                "timeline": timeline_data
            }
        )

        self.tracker.add_metrics(metrics)
        print(f"✅ 流水线并行完成: {total_time:.2f}秒, 加速比: {speedup:.2f}x")

        # 显示时间线
        self.visualizer.show_timeline_visualization(ParallelStrategy.PIPELINE_PARALLEL, timeline_data)

        return metrics

    def run_tensor_parallel(self) -> PerformanceMetrics:
        """运行张量并行模拟"""
        print(f"\n🔄 运行张量并行模拟 ({self.config.num_devices} 设备)...")

        self.tracker.start_timing()
        timeline_data = []

        total_ops = self.config.matrix_size ** 2 * self.config.batch_size

        # 张量分割：将大张量按某个维度分割
        tensor_splits = self.config.num_devices
        ops_per_split = total_ops // tensor_splits

        # 模拟张量分发
        tensor_size = total_ops * 4 / (1024**2)  # MB
        scatter_time = self.simulate_communication(tensor_size / tensor_splits)

        # 并行计算张量块
        max_compute_time = 0

        if HAS_TQDM:
            devices = tqdm(range(self.config.num_devices), desc="张量块计算")
        else:
            devices = range(self.config.num_devices)

        for device_id in devices:
            if not HAS_TQDM:
                self.visualizer.print_progress(device_id + 1, self.config.num_devices, "张量并行")

            # 每个设备处理张量的一部分
            compute_time = self.simulate_computation(
                self.config.base_compute_time / self.config.num_devices
            )
            max_compute_time = max(max_compute_time, compute_time)

            timeline_data.append({
                'device_id': device_id,
                'start_time': scatter_time,
                'duration': compute_time,
                'task_name': f'Tensor Block {device_id}'
            })

        # 模拟All-Reduce操作（张量并行特有的通信模式）
        allreduce_time = self.simulate_communication(
            tensor_size / tensor_splits * 2  # All-Reduce通信量更大
        )

        total_time = self.tracker.end_timing()
        speedup = self.tracker.calculate_speedup(total_time)
        efficiency = self.tracker.calculate_efficiency(speedup, self.config.num_devices)

        metrics = PerformanceMetrics(
            strategy=ParallelStrategy.TENSOR_PARALLEL,
            total_time=total_time,
            compute_time=max_compute_time,
            communication_time=scatter_time + allreduce_time,
            memory_usage=tensor_size / self.config.num_devices,
            throughput=total_ops / total_time,
            speedup=speedup,
            efficiency=efficiency * 100,
            details={
                "num_devices": self.config.num_devices,
                "tensor_splits": tensor_splits,
                "scatter_time": scatter_time,
                "allreduce_time": allreduce_time,
                "timeline": timeline_data
            }
        )

        self.tracker.add_metrics(metrics)
        print(f"✅ 张量并行完成: {total_time:.2f}秒, 加速比: {speedup:.2f}x")

        # 显示时间线
        self.visualizer.show_timeline_visualization(ParallelStrategy.TENSOR_PARALLEL, timeline_data)

        return metrics

    def run_all_strategies(self) -> List[PerformanceMetrics]:
        """运行所有并行策略的对比测试"""
        print("🚀 开始并行策略对比测试...")
        print("="*60)

        all_metrics = []

        # 运行所有策略
        strategies = [
            ("基准测试", self.run_baseline),
            ("数据并行", self.run_data_parallel),
            ("模型并行", self.run_model_parallel),
            ("流水线并行", self.run_pipeline_parallel),
            ("张量并行", self.run_tensor_parallel)
        ]

        for name, strategy_func in strategies:
            try:
                metrics = strategy_func()
                all_metrics.append(metrics)
                time.sleep(0.5)  # 短暂暂停以便观察
            except Exception as e:
                print(f"❌ {name} 执行失败: {e}")

        print("\n" + "="*60)
        print("🎯 所有策略测试完成!")

        # 显示综合对比
        self.visualizer.show_performance_comparison(all_metrics)

        return all_metrics


# ======================
# 教学说明和交互界面
# ======================

class ParallelEducator:
    """并行计算教学助手"""

    def __init__(self):
        self.strategies_info = {
            ParallelStrategy.DATA_PARALLEL: {
                "name": "数据并行 (Data Parallel)",
                "description": "将大批量数据分割到多个设备上并行处理",
                "advantages": ["实现简单", "适合大批量训练", "通信开销相对较小"],
                "disadvantages": ["受限于单设备内存", "模型复制开销"],
                "best_for": "大批量训练、数据密集型任务",
                "communication": "All-Reduce梯度同步"
            },
            ParallelStrategy.MODEL_PARALLEL: {
                "name": "模型并行 (Model Parallel)",
                "description": "将大模型的不同层分布到不同设备上",
                "advantages": ["突破单设备内存限制", "支持超大模型"],
                "disadvantages": ["设备利用率低", "层间通信开销大"],
                "best_for": "超大模型、内存受限场景",
                "communication": "层间激活传递"
            },
            ParallelStrategy.PIPELINE_PARALLEL: {
                "name": "流水线并行 (Pipeline Parallel)",
                "description": "将模型分阶段，不同阶段并行处理不同批次",
                "advantages": ["提高设备利用率", "减少内存需求"],
                "disadvantages": ["流水线气泡", "需要微批次调优"],
                "best_for": "深度模型、平衡内存和计算",
                "communication": "阶段间激活传递"
            },
            ParallelStrategy.TENSOR_PARALLEL: {
                "name": "张量并行 (Tensor Parallel)",
                "description": "将张量运算分割到多个设备上并行计算",
                "advantages": ["细粒度并行", "内存效率高"],
                "disadvantages": ["通信开销大", "实现复杂"],
                "best_for": "Transformer模型、注意力机制",
                "communication": "All-Reduce张量同步"
            }
        }

    def explain_strategy(self, strategy: ParallelStrategy):
        """解释特定并行策略"""
        if strategy == ParallelStrategy.NONE:
            print("\n📚 基准测试（无并行）")
            print("="*50)
            print("这是传统的单设备计算方式，所有计算都在一个设备上顺序执行。")
            print("虽然没有并行加速，但也没有通信开销，是其他策略的性能基准。")
            return

        info = self.strategies_info.get(strategy)
        if not info:
            return

        print(f"\n📚 {info['name']}")
        print("="*50)
        print(f"原理: {info['description']}")
        print(f"\n✅ 优点:")
        for advantage in info['advantages']:
            print(f"  • {advantage}")
        print(f"\n❌ 缺点:")
        for disadvantage in info['disadvantages']:
            print(f"  • {disadvantage}")
        print(f"\n🎯 适用场景: {info['best_for']}")
        print(f"🔄 通信模式: {info['communication']}")

    def show_performance_tips(self, metrics: List[PerformanceMetrics]):
        """显示性能优化建议"""
        print("\n💡 性能优化建议")
        print("="*50)

        best_strategy = max(metrics, key=lambda m: m.speedup)
        worst_strategy = min(metrics, key=lambda m: m.speedup)

        print(f"🏆 最佳策略: {best_strategy.strategy.value}")
        print(f"   加速比: {best_strategy.speedup:.2f}x")
        print(f"   效率: {best_strategy.efficiency:.1f}%")

        print(f"\n⚠️  需要优化: {worst_strategy.strategy.value}")
        print(f"   加速比: {worst_strategy.speedup:.2f}x")
        print(f"   效率: {worst_strategy.efficiency:.1f}%")

        # 根据结果给出建议
        print(f"\n📋 优化建议:")
        for metric in metrics:
            if metric.strategy == ParallelStrategy.NONE:
                continue

            if metric.efficiency < 50:
                print(f"  • {metric.strategy.value}: 效率较低，考虑减少设备数量或优化通信")
            elif metric.communication_time > metric.compute_time:
                print(f"  • {metric.strategy.value}: 通信开销过大，考虑增加计算强度或优化网络")
            elif metric.speedup > metric.details.get('num_devices', 1) * 0.8:
                print(f"  • {metric.strategy.value}: 表现优秀，可以考虑扩展到更多设备")


def interactive_demo():
    """交互式演示界面"""
    print("🎓 并行计算教学演示系统")
    print("="*60)
    print("欢迎使用并行计算模拟器！这个工具将帮助您理解不同并行策略的特点。")

    educator = ParallelEducator()

    while True:
        print("\n📋 请选择操作:")
        print("1. 运行完整对比测试")
        print("2. 运行单个策略测试")
        print("3. 自定义参数测试")
        print("4. 查看策略详细说明")
        print("5. 退出")

        try:
            choice = input("\n请输入选择 (1-5): ").strip()

            if choice == '1':
                # 完整对比测试
                config = SimulationConfig()
                simulator = ParallelSimulator(config)

                print(f"\n🔧 使用默认配置:")
                print(f"  矩阵大小: {config.matrix_size}x{config.matrix_size}")
                print(f"  批次大小: {config.batch_size}")
                print(f"  设备数量: {config.num_devices}")

                metrics = simulator.run_all_strategies()
                educator.show_performance_tips(metrics)

            elif choice == '2':
                # 单个策略测试
                print("\n选择要测试的策略:")
                print("1. 基准测试（无并行）")
                print("2. 数据并行")
                print("3. 模型并行")
                print("4. 流水线并行")
                print("5. 张量并行")

                strategy_choice = input("请输入选择 (1-5): ").strip()
                strategy_map = {
                    '1': ParallelStrategy.NONE,
                    '2': ParallelStrategy.DATA_PARALLEL,
                    '3': ParallelStrategy.MODEL_PARALLEL,
                    '4': ParallelStrategy.PIPELINE_PARALLEL,
                    '5': ParallelStrategy.TENSOR_PARALLEL
                }

                if strategy_choice in strategy_map:
                    strategy = strategy_map[strategy_choice]
                    educator.explain_strategy(strategy)

                    config = SimulationConfig()
                    simulator = ParallelSimulator(config)

                    if strategy == ParallelStrategy.NONE:
                        metrics = simulator.run_baseline()
                    elif strategy == ParallelStrategy.DATA_PARALLEL:
                        metrics = simulator.run_data_parallel()
                    elif strategy == ParallelStrategy.MODEL_PARALLEL:
                        metrics = simulator.run_model_parallel()
                    elif strategy == ParallelStrategy.PIPELINE_PARALLEL:
                        metrics = simulator.run_pipeline_parallel()
                    elif strategy == ParallelStrategy.TENSOR_PARALLEL:
                        metrics = simulator.run_tensor_parallel()

            elif choice == '3':
                # 自定义参数测试
                print("\n🔧 自定义参数:")
                try:
                    matrix_size = int(input(f"矩阵大小 (默认1000): ") or "1000")
                    batch_size = int(input(f"批次大小 (默认32): ") or "32")
                    num_devices = int(input(f"设备数量 (默认4): ") or "4")

                    config = SimulationConfig(
                        matrix_size=matrix_size,
                        batch_size=batch_size,
                        num_devices=num_devices
                    )

                    simulator = ParallelSimulator(config)
                    metrics = simulator.run_all_strategies()
                    educator.show_performance_tips(metrics)

                except ValueError:
                    print("❌ 输入无效，请输入数字")

            elif choice == '4':
                # 查看策略说明
                print("\n选择要了解的策略:")
                print("1. 数据并行")
                print("2. 模型并行")
                print("3. 流水线并行")
                print("4. 张量并行")

                info_choice = input("请输入选择 (1-4): ").strip()
                info_map = {
                    '1': ParallelStrategy.DATA_PARALLEL,
                    '2': ParallelStrategy.MODEL_PARALLEL,
                    '3': ParallelStrategy.PIPELINE_PARALLEL,
                    '4': ParallelStrategy.TENSOR_PARALLEL
                }

                if info_choice in info_map:
                    educator.explain_strategy(info_map[info_choice])

            elif choice == '5':
                print("👋 感谢使用并行计算教学演示系统！")
                break

            else:
                print("❌ 无效选择，请重新输入")

        except KeyboardInterrupt:
            print("\n\n👋 程序已退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")


# ======================
# 主程序入口
# ======================

if __name__ == "__main__":
    # 检查依赖
    missing_deps = []
    if not HAS_MATPLOTLIB:
        missing_deps.append("matplotlib")
    if not HAS_TQDM:
        missing_deps.append("tqdm")

    if missing_deps:
        print("⚠️  建议安装以下依赖以获得最佳体验:")
        for dep in missing_deps:
            print(f"   pip install {dep}")
        print()

    # 启动交互式演示
    interactive_demo()
import matplotlib.pyplot as plt
import numpy as np

# 系统参数
peak_performance = 1000  # GFLOPs/s
peak_bandwidth = 100  # GB/s
bandwidth_ceiling = 50  # GB/s (由于通信开销)

# 生成 x 轴：算术强度
x = np.logspace(-2, 2, 100)  # 从 0.01 到 100 FLOPs/byte

# 计算理想 Roofline
y_ideal = np.minimum(peak_performance, peak_bandwidth * x * 1e-3)  # 转换 GB/s 到 GFLOPs/s

# 计算带宽天花板（通信开销）
y_ceiling = np.minimum(peak_performance, bandwidth_ceiling * x * 1e-3)

# 绘制理想 Roofline
plt.plot(x, y_ideal, label='理想 Roofline', color='black')

# 绘制带宽天花板（通信开销）
plt.plot(x, y_ceiling, label='带宽天花板 (通信开销)', color='blue', linestyle='--')

# 添加峰值性能和岭点标签
plt.axhline(y=peak_performance, color='gray', linestyle='--', label='峰值性能')
plt.axvline(x=peak_performance / (peak_bandwidth * 1e-3), color='gray', linestyle='--', label='岭点 (理想)')

# 示例内核数据点
kernel1_ai = 0.1  # FLOPs/byte
kernel1_perf = 10  # GFLOPs/s
kernel2_ai = 10  # FLOPs/byte
kernel2_perf = 500  # GFLOPs/s

plt.scatter([kernel1_ai, kernel2_ai], [kernel1_perf, kernel2_perf], color='red', label='内核')

# 设置标签和标题
plt.xscale('log')
plt.yscale('log')
plt.xlabel('算术强度 (FLOPs/byte)')
plt.ylabel('性能 (GFLOPs/s)')
plt.title('Roofline 模型 (包含通信开销)')
plt.legend()
plt.grid(True, which="both", ls="--")
plt.show()

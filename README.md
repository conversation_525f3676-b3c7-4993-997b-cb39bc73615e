# 并行计算教学演示系统

这是一个帮助初学者理解不同并行计算策略的教学工具。通过在单机单GPU环境下模拟各种并行策略，让用户直观地了解并行计算的原理和性能特点。

## 功能特点

### 🎯 支持的并行策略
- **数据并行 (Data Parallel)**: 将大批量数据分割到多个设备上并行处理
- **模型并行 (Model Parallel)**: 将大模型的不同层分布到不同设备上
- **流水线并行 (Pipeline Parallel)**: 将模型分阶段，不同阶段并行处理不同批次
- **张量并行 (Tensor Parallel)**: 将张量运算分割到多个设备上并行计算

### 📊 可视化功能
- 性能对比图表（需要matplotlib）
- 执行时间线可视化
- 加速比和效率分析
- 实时进度显示（支持tqdm）

### 🎓 教学功能
- 详细的策略原理解释
- 优缺点分析
- 适用场景说明
- 性能优化建议

## 安装依赖

基础运行（无可视化）：
```bash
# 无需额外依赖，使用Python标准库
python parallel.py
```

完整功能（推荐）：
```bash
pip install matplotlib tqdm
python parallel.py
```

## 使用方法

### 1. 启动程序
```bash
python parallel.py
```

### 2. 选择功能
程序提供5种操作模式：

1. **运行完整对比测试** - 一次性测试所有并行策略并对比性能
2. **运行单个策略测试** - 深入了解特定并行策略
3. **自定义参数测试** - 调整矩阵大小、批次大小、设备数量等参数
4. **查看策略详细说明** - 学习各种并行策略的原理和特点
5. **退出程序**

### 3. 示例输出

#### 性能对比表格
```
================================================================================
并行策略性能对比分析
================================================================================
策略              时间(s)      加速比        效率(%)      吞吐量       
--------------------------------------------------------------------------------
无并行             2.01       1.00       100.0      15920398.0
数据并行            0.85       2.36       59.1       37634819.8
模型并行            1.45       1.39       34.7       22080552.4
流水线并行           1.20       1.68       41.9       26667000.0
张量并行            0.92       2.18       54.6       34782608.7
================================================================================
```

#### 策略详细说明
```
📚 数据并行 (Data Parallel)
==================================================
原理: 将大批量数据分割到多个设备上并行处理

✅ 优点:
  • 实现简单
  • 适合大批量训练
  • 通信开销相对较小

❌ 缺点:
  • 受限于单设备内存
  • 模型复制开销

🎯 适用场景: 大批量训练、数据密集型任务
🔄 通信模式: All-Reduce梯度同步
```

## 配置参数

可以通过修改 `SimulationConfig` 类来调整模拟参数：

```python
@dataclass
class SimulationConfig:
    matrix_size: int = 1000        # 矩阵大小
    batch_size: int = 32           # 批次大小
    num_devices: int = 4           # 设备数量
    model_layers: int = 8          # 模型层数
    pipeline_stages: int = 4       # 流水线阶段数
    base_compute_time: float = 2.0 # 基础计算时间
    communication_overhead: float = 0.05  # 通信开销比例
```

## 教学价值

### 🎯 学习目标
- 理解不同并行策略的工作原理
- 掌握并行计算的性能评估方法
- 学会根据场景选择合适的并行策略
- 了解并行计算中的通信开销问题

### 📚 适用对象
- 深度学习初学者
- 并行计算课程学生
- 想要了解分布式训练的开发者
- 需要优化模型训练性能的研究人员

## 技术实现

### 🔧 "Fake Parallel" 原理
由于在单机环境下无法真正实现并行，本工具通过以下方式模拟：

1. **时间分片**: 用循环模拟并行执行，记录理论执行时间
2. **性能建模**: 基于理论公式计算加速比和效率
3. **通信模拟**: 模拟真实的数据传输和同步开销
4. **随机性**: 添加噪声模拟真实环境的性能波动

### 📊 性能指标
- **加速比 (Speedup)**: 串行时间 / 并行时间
- **并行效率 (Efficiency)**: 加速比 / 处理器数量
- **吞吐量 (Throughput)**: 每秒处理的操作数
- **通信开销**: 数据传输和同步的时间成本

## 扩展功能

### 🔮 未来改进
- [ ] 支持更多并行策略（如混合并行）
- [ ] 添加网络拓扑模拟
- [ ] 支持异构设备模拟
- [ ] 添加内存使用分析
- [ ] 支持实际GPU测试对比

### 🛠️ 自定义扩展
用户可以通过继承 `ParallelSimulator` 类来添加自定义的并行策略：

```python
class CustomSimulator(ParallelSimulator):
    def run_custom_strategy(self) -> PerformanceMetrics:
        # 实现自定义并行策略
        pass
```

## 许可证

MIT License - 欢迎学习和改进！

## 贡献

欢迎提交Issue和Pull Request来改进这个教学工具。
